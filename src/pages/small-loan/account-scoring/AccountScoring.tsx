import { AccountScoringViewTypes } from 'app-constants';
import { AppLoader } from 'components';
import { useSharedAccountScoringPageLogic } from 'hooks/shared';

import styles from './AccountScoring.module.scss';
import { AccountScoringInitialView } from './AccountScoringInitialView';
import { AccountScoringStepperView } from './AccountScoringStepperView';

const Page = () => {
  const {
    accountScoringPageLoaded,
    accountScoringSteps,
    onAccountStatementUploaded,
    onAccessBankButtonClick,
    accountScoringViewType,
  } = useSharedAccountScoringPageLogic();

  if (!accountScoringPageLoaded) {
    return <AppLoader isRelative />;
  }

  const renderAccountScoringView = () => {
    switch (accountScoringViewType) {
      case AccountScoringViewTypes.initial:
        return (
          <AccountScoringInitialView
            {...{
              accountScoringSteps,
              onAccountStatementUploaded,
              onAccessBankButtonClick,
            }}
          />
        );
      case AccountScoringViewTypes.stepper:
        return (
          <AccountScoringStepperView
            {...{
              accountScoringSteps,
              onAccountStatementUploaded,
              onAccessBankButtonClick,
            }}
          />
        );
      default:
        return null;
    }
  };

  return <div className={styles.page}>{renderAccountScoringView()}</div>;
};

export default Page;
