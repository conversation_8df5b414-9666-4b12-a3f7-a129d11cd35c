import { zodResolver } from '@hookform/resolvers/zod';
import {
  ELIGIBILITY_STATES,
  FormFieldNames,
  GoogleAnalyticsEvents,
  LocizeNamespaces,
  PageAttributeNames,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { isEtRegion, regionPhonePrefix } from 'environment';
import { useGetOccupationCategoriesDefault } from 'hooks';
import { useGetPageAttributesSuspense } from 'hooks/use-get-page-attributes-suspense';
import { useGetPageUrl } from 'hooks/use-get-page-url';
import { useGetUserSuspense } from 'hooks/use-get-user-suspence';
import { useLogCreditAccountAction } from 'hooks/use-log-credit-account-action';
import { useUpdateUserInfoExtra } from 'hooks/use-update-user-info-extra';
import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  convertPageAttributeNamesToObject,
  extractValidationErrors,
  filterObjectByExistingKeysInObject,
  formatEmploymentDateValueToOptionValue2,
  formatUserToContactExtraPageDataFormat,
  getApplicationStatusForMarketing,
  getEmploymentDateOptions,
  getOccupationCategoriesOptions,
  roundNumberUpByTwoDecimals,
} from 'services';
import { processNumberOfDependentsForReactHookForm } from 'utils/number-of-dependents';
import { processGqlFormValidationErrors } from 'utils/parseGraphQLError';
import * as z from 'zod';

const ContactExtraFormType = z.object({
  [FormFieldNames.netIncomeMonthly]: z.number().nullable(),
  [FormFieldNames.expenditureMonthly]: z.number().nullable(),
  [FormFieldNames.monthlyLivingExpenses]: z.number().nullable(),
  [FormFieldNames.numberOfDependents]: isEtRegion
    ? z.number({ required_error: 'This field is required' })
    : z.number().nullable(),
  [FormFieldNames.totalExpenses]: z.number().nullable(),
  [FormFieldNames.employmentDate]: z.string().nullable(),
  [FormFieldNames.planningNewDebts]: z.number().nullable(),
  [FormFieldNames.futureReducedEarnings]: z.number().nullable(),
  [FormFieldNames.ultimateBeneficialOwner]: z.boolean(),
  [FormFieldNames.occupationCategory]: z.string().nullable(),
  [FormFieldNames.overdueDebt]: z.number().nullable(),
});

export type ContactExtraFormType = z.infer<typeof ContactExtraFormType>;

export const useContactExtraPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);

  const { trackGoogleAnalyticsEvent } = useRootContext();

  const { user, quietUserRefetch } = useGetUserSuspense();
  const { getPageUrlAndNavigate } = useGetPageUrl();
  const { pageAttributes } = useGetPageAttributesSuspense();

  const visiblePageAttributes = useMemo(
    () => convertPageAttributeNamesToObject(pageAttributes),
    [pageAttributes],
  );

  const { occupationCategories } = useGetOccupationCategoriesDefault({
    skip: !visiblePageAttributes[PageAttributeNames.occupationCategoryDropdown],
  });

  const { logAction } = useLogCreditAccountAction();

  const { updateUserInfoExtra, userInfoExtraUpdateError } =
    useUpdateUserInfoExtra();

  const {
    ultimateBeneficialOwner,
    futureReducedEarnings,
    planningNewDebts,
    employmentDate,
    numberOfDependents,
    monthlyLivingExpenses,
    expenditureMonthly,
    netIncomeMonthly,
    occupationCategory,
    productId,
    phoneAreaCode,
    eligibilityState,
    applicationUserInfoId,
    overdueDebt,
  } = useMemo(
    () => formatUserToContactExtraPageDataFormat({ ...user }),
    [user],
  );

  const employmentDateOptions = getEmploymentDateOptions(t);

  const form = useForm<ContactExtraFormType>({
    resolver: zodResolver(ContactExtraFormType),
    defaultValues: {
      [FormFieldNames.netIncomeMonthly]: netIncomeMonthly ?? null,
      [FormFieldNames.expenditureMonthly]: expenditureMonthly ?? null,
      [FormFieldNames.monthlyLivingExpenses]: monthlyLivingExpenses ?? null,
      [FormFieldNames.numberOfDependents]: isEtRegion
        ? (numberOfDependents ?? undefined)
        : (numberOfDependents ?? null),
      [FormFieldNames.totalExpenses]: roundNumberUpByTwoDecimals(
        Number(monthlyLivingExpenses ?? 0) + Number(expenditureMonthly ?? 0),
      ),
      [FormFieldNames.employmentDate]:
        formatEmploymentDateValueToOptionValue2(
          employmentDate,
          employmentDateOptions,
        ) ?? '',
      [FormFieldNames.planningNewDebts]: planningNewDebts ?? 0,
      [FormFieldNames.overdueDebt]: overdueDebt ?? 0,
      [FormFieldNames.futureReducedEarnings]: futureReducedEarnings ?? 0,
      [FormFieldNames.ultimateBeneficialOwner]: ultimateBeneficialOwner ?? true,
      [FormFieldNames.occupationCategory]: occupationCategory,
    },
  });

  const userInfoExtraValidationErrors = extractValidationErrors(
    userInfoExtraUpdateError,
  );

  const occupationCategoryOptions = getOccupationCategoriesOptions(
    t,
    occupationCategories,
  );

  const onContactExtraFormSubmit = async ({
    number_of_dependents,
    expenditure_monthly,
    monthly_living_expenses,
    net_income_monthly,
    planning_new_debts,
    employment_date,
    future_reduced_earnings,
    ultimate_beneficial_owner,
    occupation_category,
    overdue_debt,
  }: ContactExtraFormType) => {
    try {
      const variablesFilteredByVisiblePageAttributes =
        filterObjectByExistingKeysInObject(
          {
            number_of_dependents:
              processNumberOfDependentsForReactHookForm(number_of_dependents),
            expenditure_monthly,
            monthly_living_expenses,
            net_income_monthly,
            planning_new_debts: planning_new_debts ?? 0,
            employment_date,
            future_reduced_earnings: future_reduced_earnings ?? 0,
            ultimate_beneficial_owner,
            occupation_category,
            overdue_debt: overdue_debt ?? 0,
          },
          form.control._fields,
        );

      const appEligibilityState =
        eligibilityState ?? ELIGIBILITY_STATES.pending;
      await updateUserInfoExtra({
        application_user_info_id: applicationUserInfoId ?? 0,
        reject_when_necessary: true,
        extra_income: 0,
        ...variablesFilteredByVisiblePageAttributes,
      });

      await quietUserRefetch();

      await getPageUrlAndNavigate(true);
      trackGoogleAnalyticsEvent(GoogleAnalyticsEvents.contactExtraCompleted, {
        status: getApplicationStatusForMarketing(appEligibilityState),
      });
    } catch (error) {
      if ((error as Error)?.message === 'Unauthorized') {
        await getPageUrlAndNavigate(true);
      }

      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  useEffect(() => {
    // LOGGING ACTION
    if (productId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.choosingContactExtraInfo,
      });
    }
  }, [productId]);

  return useMemo(
    () => ({
      onContactExtraFormSubmit,
      visiblePageAttributes,
      userInfoExtraValidationErrors,
      occupationCategoryOptions,
      employmentDateOptions,
      phonePrefix: phoneAreaCode ? `+${phoneAreaCode}` : regionPhonePrefix,
      form,
    }),
    [
      onContactExtraFormSubmit,
      visiblePageAttributes,
      userInfoExtraValidationErrors,
      occupationCategoryOptions,
      employmentDateOptions,
      phoneAreaCode,
      form,
    ],
  );
};
