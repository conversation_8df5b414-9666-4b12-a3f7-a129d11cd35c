import { NetworkStatus, type SuspenseQueryHookOptions } from '@apollo/client';
import {
  type MeWithLegalPeopleQuery,
  type MeWithLegalPeopleQueryVariables,
  useMeWithLegalPeopleSuspenseQuery,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';

export const useGetUserWithLegalSuspense = (
  options?: SuspenseQueryHookOptions<
    MeWithLegalPeopleQuery,
    MeWithLegalPeopleQueryVariables
  >,
) => {
  const { data, error, networkStatus } = useMeWithLegalPeopleSuspenseQuery({
    context: { apiVersion: AppApiVersions.core },
    ...options,
  });

  return {
    userWithLegal: data?.me ?? null,
    userWithLegalLoading: networkStatus === NetworkStatus.loading,
    userWithLegalError: error,
  };
};
