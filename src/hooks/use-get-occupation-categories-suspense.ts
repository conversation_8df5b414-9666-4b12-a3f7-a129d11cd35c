import { NetworkStatus, type SuspenseQueryHookOptions } from '@apollo/client';
import {
  type GetOccupationCategoriesQuery,
  type GetOccupationCategoriesQueryVariables,
  useGetOccupationCategoriesSuspenseQuery,
} from 'api/core/generated';
import { AppApiVersions } from 'app-constants';

export const useGetOccupationCategoriesSuspense = (
  options?: SuspenseQueryHookOptions<
    GetOccupationCategoriesQuery,
    GetOccupationCategoriesQueryVariables
  >,
) => {
  const { data, error, networkStatus } = useGetOccupationCategoriesSuspenseQuery({
    context: { apiVersion: AppApiVersions.core },
    ...options,
  });

  return {
    occupationCategories: data?.categories,
    occupationCategoriesLoading: networkStatus === NetworkStatus.loading,
    occupationCategoriesError: error,
  };
};
