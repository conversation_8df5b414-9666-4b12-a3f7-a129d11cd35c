import type { CreditAccount } from 'api/core/generated';
import {
  AccountScoringViewType,
  AppProductType,
  AppRoutePaths,
  AppSearchParams,
  CreditLineRoutePaths,
  EMTA_STATUSES,
  IncomeSourcesCheckServices,
  LocalStorageKeys,
  LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS,
  LocizeNamespaces,
  PURCHASE_FLOW_LOG_ACTIONS,
  SmallLoanRoutePaths,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetEmtaConsent } from 'hooks/use-get-emta-consent';
import { useGetProductTypeByUrl } from 'hooks/use-get-product-type-by-url';
import { useGetUserSuspense } from 'hooks/use-get-user-suspence';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useLogCreditAccountAction } from 'hooks/use-log-credit-account-action';
import { useStoreAccountScoringInvitation } from 'hooks/use-store-account-scoring-invitation';
import { useEffectOnce } from 'hooks/utils';
import type { AnyObject } from 'models';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useSearchParams } from 'react-router-dom';
import { replaceOrAddQueryParamsInUrl, setToStorage } from 'services';
import { v4 as uuid } from 'uuid';

export const useSharedEmtaConsentPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.emtaConsent);
  const { search, pathname } = useLocation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const emtaStatus = searchParams.get(AppSearchParams.emtaStatus) ?? '';
  const productType = useGetProductTypeByUrl();

  const { user, getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();

  const {
    getEmtaConsent,
    startEmtaConsentPolling,
    stopEmtaConsentPolling,
    emtaConsent,
  } = useGetEmtaConsent();

  const { accountScoringInvitationData, storeAccountScoringInvitation } =
    useStoreAccountScoringInvitation();

  // State for small-loan flow
  const [
    selectedIncomeSourceCheckService,
    setSelectedIncomeSourceCheckService,
  ] = useState(IncomeSourcesCheckServices.emta);

  // State for credit-line flow
  const [isRedirectingToAccountScoring, setIsRedirectingToAccountScoring] =
    useState(false);
  const [isRedirectingToEmta, setIsRedirectingToEmta] = useState(false);

  // Determine if this is a credit-line or small-loan flow
  const isCreditLineFlow = productType === AppProductType.CREDIT_LINE;
  const isSmallLoanFlow = productType === AppProductType.SMALL_LOAN;

  // Get appropriate data based on product type
  const { getApplication } = useGetCurrentApplication({
    onCompleted: (data) => {
      if (isSmallLoanFlow) {
        requestEmtaConsent({
          application_id: data?.application?.id,
        });
      }
    },
  });

  const { user: userSuspense } = useGetUserSuspense();
  const creditAccount = isCreditLineFlow
    ? ((userSuspense?.credit_accounts ?? [{}])[0] as CreditAccount)
    : null;

  // Logging hooks
  const { logAction: logApplicationAction } = useLogApplicationAction();
  const { logAction: logCreditAccountAction } = useLogCreditAccountAction();

  const logAction = isCreditLineFlow
    ? logCreditAccountAction
    : logApplicationAction;
  const productId = isCreditLineFlow ? creditAccount?.id : null;

  const requestEmtaConsent = (productVariables: AnyObject) => {
    const returnUrl = replaceOrAddQueryParamsInUrl({
      returnWithBase: true,
      url: `${pathname}${search}`,
      params: {
        [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
      },
    });

    if (!user?.id) {
      throw new Error('User id is not defined');
    }

    const baseParams = {
      user_id: user.id,
      return_url: returnUrl,
    };

    if (isCreditLineFlow) {
      getEmtaConsent({
        ...baseParams,
        credit_account_id: creditAccount?.id,
      });
    } else {
      getEmtaConsent({
        ...baseParams,
        ...productVariables,
      });
    }
  };

  const onEmtaRedirectButtonClick = () => {
    if (emtaConsent?.url) {
      setIsRedirectingToEmta(true);
      window.location.href = emtaConsent?.url;

      if (productId) {
        logAction({
          productId,
          action: PURCHASE_FLOW_LOG_ACTIONS.emtaConsentRedirect,
        });
      }

      setTimeout(() => {
        setIsRedirectingToEmta(false);
      }, 2000);
    }
  };

  const onAccountScoringRedirectButtonClick = () => {
    if (isCreditLineFlow) {
      // Credit-line flow: redirect to external URL
      if (accountScoringInvitationData?.response_url) {
        setIsRedirectingToAccountScoring(true);

        setToStorage(
          LocalStorageKeys.accountScoringView,
          AccountScoringViewType.SCORING,
        );

        window.location.href = accountScoringInvitationData.response_url;

        if (creditAccount?.id) {
          logAction({
            productId: creditAccount.id,
            action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringRedirected,
          });
        }

        setTimeout(() => {
          setIsRedirectingToAccountScoring(false);
        }, 2000);
      }
    } else {
      // Small-loan flow: navigate to internal page
      const routePath = isSmallLoanFlow
        ? SmallLoanRoutePaths.ACCOUNT_SCORING
        : CreditLineRoutePaths.ACCOUNT_SCORING;

      getPageUrlAndNavigate(null, {
        customCurrentPageUrl: `/${AppRoutePaths.SMALL_LOAN}/${routePath}${search}`,
      });
    }
  };

  const onContinueButtonClick = () => {
    if (!isSmallLoanFlow) return;

    switch (selectedIncomeSourceCheckService) {
      case IncomeSourcesCheckServices.emta:
        if (emtaConsent?.url) {
          window.location.href = emtaConsent?.url;
        }
        break;
      case IncomeSourcesCheckServices.accountScoring:
        onAccountScoringRedirectButtonClick();
        break;
      default:
        break;
    }
  };

  const goBackToScoringMethods = () => {
    if (!isCreditLineFlow) return;

    const newSearchParams = new URLSearchParams(search);
    newSearchParams.delete(AppSearchParams.emtaStatus);

    navigate({
      pathname,
      search: newSearchParams.toString(),
    });
  };

  // Income sources options for small-loan flow
  const incomeSourcesCheckServicesOptions = isSmallLoanFlow
    ? [
        {
          id: uuid(),
          onClick: () =>
            setSelectedIncomeSourceCheckService(
              IncomeSourcesCheckServices.emta,
            ),
          isActive:
            selectedIncomeSourceCheckService ===
            IncomeSourcesCheckServices.emta,
          title: t(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaConsentButtonTitle),
          description: t(
            LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.emtaConsentButtonDescription,
          ),
        },
        {
          id: uuid(),
          onClick: () =>
            setSelectedIncomeSourceCheckService(
              IncomeSourcesCheckServices.accountScoring,
            ),
          isActive:
            selectedIncomeSourceCheckService ===
            IncomeSourcesCheckServices.accountScoring,
          title: t(
            LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.accountScoringButtonTitle,
          ),
          description: t(
            LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.accountScoringButtonDescription,
          ),
        },
      ]
    : [];

  // Initialize data based on product type
  useEffectOnce(() => {
    if (isSmallLoanFlow) {
      getApplication();
    } else if (isCreditLineFlow) {
      if (!userSuspense) {
        throw new Error('User is missing');
      }

      const email = userSuspense.email;
      const language = userSuspense.language_abbr;

      if (!email) {
        throw new Error('User email is missing');
      }

      storeAccountScoringInvitation({
        user_id: userSuspense.id,
        user_mail: email,
        user_name: `${userSuspense.profile?.first_name ?? ''} ${
          userSuspense.profile?.last_name ?? ''
        }`,
        redirect_url: `/${AppRoutePaths.CREDIT_LINE}/${CreditLineRoutePaths.ACCOUNT_SCORING}${search}`,
        language,
        credit_account_id: creditAccount?.id,
        application_id: null,
      });

      requestEmtaConsent({
        credit_account_id: creditAccount?.id,
      });
    }
  });

  // Handle EMTA status polling
  useEffect(() => {
    if (emtaStatus === EMTA_STATUSES.loading) {
      const returnUrl = replaceOrAddQueryParamsInUrl({
        returnWithBase: true,
        url: `${pathname}${search}`,
        params: {
          [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
        },
      });

      if (!user?.id) {
        throw new Error('User id is not defined');
      }

      startEmtaConsentPolling({
        user_id: user.id,
        return_url: returnUrl,
      });
    }
  }, [emtaStatus]);

  // Handle EMTA completion
  useEffect(() => {
    if (emtaConsent?.valid_until) {
      const customCurrentPageUrl = replaceOrAddQueryParamsInUrl({
        returnWithBase: true,
        url: `${pathname}${search}`,
        params: {
          [AppSearchParams.emtaStatus]: EMTA_STATUSES.finished,
        },
      });

      stopEmtaConsentPolling();
      getPageUrlAndNavigate(true, { customCurrentPageUrl });
    }
  }, [emtaConsent?.valid_until]);

  return {
    // Common props
    onEmtaRedirectButtonClick,
    onAccountScoringRedirectButtonClick,
    returnedFromEmtaService: emtaStatus === EMTA_STATUSES.loading,
    emtaConsentUrl: emtaConsent?.url,
    emtaConsentPageLoaded: !!emtaConsent,

    // Credit-line specific props
    emtaConsentButtonDisabled: !emtaConsent?.url,
    accountScoringButtonDisabled: !accountScoringInvitationData?.response_url,
    isRedirectingToAccountScoring,
    isRedirectingToEmta,
    goBackToScoringMethods,

    // Small-loan specific props
    onContinueButtonClick,
    incomeSourcesCheckServicesOptions,
    selectedIncomeSourceCheckService,
    setSelectedIncomeSourceCheckService,
    emtaConsentPageProcessing:
      !emtaConsent ||
      pageUrlAndNavigationProcessing ||
      emtaStatus === EMTA_STATUSES.loading,
  };
};
