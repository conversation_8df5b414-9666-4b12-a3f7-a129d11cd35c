import { AccountStatementProviderType } from 'api/core/generated';
import {
  AccountScoringViewType,
  AccountScoringViewTypes,
  AppProductType,
  AppSearchParams,
  EMTA_STATUSES,
  LocalStorageKeys,
  PURCHASE_FLOW_LOG_ACTIONS,
} from 'app-constants';
import { useRootContext } from 'context/root';
import { useAccountScoringSteps } from 'hooks/use-account-scoring-steps';
import { useGetCurrentApplication } from 'hooks/use-get-current-application';
import { useGetEmtaConsent } from 'hooks/use-get-emta-consent';
import { useGetProductTypeByUrl } from 'hooks/use-get-product-type-by-url';
import { useGetUserSuspense } from 'hooks/use-get-user-suspence';
import { useLogApplicationAction } from 'hooks/use-log-application-action';
import { useLogCreditAccountAction } from 'hooks/use-log-credit-account-action';
import { usePollAccountScoringInvitation } from 'hooks/use-poll-account-scoring-invitation';
import { usePollAccountScoringStatement } from 'hooks/use-poll-account-scoring-statement';
import { useStoreAccountScoringInvitation } from 'hooks/use-store-account-scoring-invitation';
import { useUploadAccountStatement } from 'hooks/use-upload-account-statement';
import { useEffectOnce } from 'hooks/utils';
import { useEffect, useMemo, useState } from 'react';
import { useLocation, useSearchParams } from 'react-router-dom';
import {
  formatApplicationToAccountScoringPageDataFormat,
  formatUserToAccountScoringPageDataFormat,
  getDefaultAccountScoringViewType,
  getFromStorage,
  removeFromStorage,
  replaceOrAddQueryParamsInUrl,
  setToStorage,
} from 'services';

export const useSharedAccountScoringPageLogic = () => {
  const { pathname, search } = useLocation();
  const [searchParams] = useSearchParams();
  const emtaStatus = searchParams.get(AppSearchParams.emtaStatus) ?? '';
  const productType = useGetProductTypeByUrl();

  const { getPageUrlAndNavigate, pageUrlAndNavigationProcessing } =
    useRootContext();

  // Determine if this is a credit-line or small-loan flow
  const isCreditLineFlow = productType === AppProductType.CREDIT_LINE;
  const isSmallLoanFlow = productType === AppProductType.SMALL_LOAN;

  // Get user data
  const { user } = useGetUserSuspense();

  // Get application data for small-loan flow
  const { getApplication, application } = useGetCurrentApplication();

  // Format user data based on product type
  const userDataFormatted = isCreditLineFlow
    ? formatUserToAccountScoringPageDataFormat(user)
    : formatApplicationToAccountScoringPageDataFormat(application);

  const { email, userName, language, productId, userId, productIdVariables } =
    userDataFormatted;

  // Account scoring view type state
  const [accountScoringViewType, setAccountScoringViewType] = useState(
    getFromStorage(LocalStorageKeys.accountScoringView) ??
      (isCreditLineFlow
        ? AccountScoringViewType.APPLICATION
        : getDefaultAccountScoringViewType()),
  );

  // Loading states
  const [isRedirectingToAccountScoring, setIsRedirectingToAccountScoring] =
    useState(false);
  const [isRedirectingToEmta, setIsRedirectingToEmta] = useState(false);

  // Hooks
  const {
    accountScoringSteps,
    finishAccountScoringSteps,
    retriggerAccountScoringSteps,
    triggerAccountScoringSteps,
  } = useAccountScoringSteps(accountScoringViewType);

  const { uploadAccountStatement, uploadAccountStatementProcessing } =
    useUploadAccountStatement();
  const { accountScoringInvitationData, storeAccountScoringInvitation } =
    useStoreAccountScoringInvitation();
  const { accountScoringInvitation } = usePollAccountScoringInvitation();
  const { accountScoringStatement, startAccountScoringStatementPolling } =
    usePollAccountScoringStatement();
  const { getEmtaConsent, startEmtaConsentPolling, emtaConsent } =
    useGetEmtaConsent();

  // Logging hooks
  const { logAction: logApplicationAction } = useLogApplicationAction();
  const { logAction: logCreditAccountAction } = useLogCreditAccountAction();

  const logAction = isCreditLineFlow
    ? logCreditAccountAction
    : logApplicationAction;

  // Account statement upload handler
  const onAccountStatementUploaded = (statement: File) => {
    if (!userId) {
      throw new Error('User ID is missing');
    }

    uploadAccountStatement({
      user_id: userId,
      provider: AccountStatementProviderType.ACCOUNTSCORING,
      send_email: false,
      statement,
      ...productIdVariables,
    }).then(() => {
      if (isSmallLoanFlow) {
        setAccountScoringViewType(AccountScoringViewTypes.stepper);
      }
      retriggerAccountScoringSteps();
    });
  };

  // Account scoring redirect handler
  const onAccountScoringRedirectButtonClick = () => {
    if (isCreditLineFlow) {
      // Credit-line flow: redirect to external URL
      if (accountScoringInvitationData?.response_url) {
        setIsRedirectingToAccountScoring(true);

        setToStorage(
          LocalStorageKeys.accountScoringView,
          AccountScoringViewType.SCORING,
        );

        window.location.href = accountScoringInvitationData.response_url;

        if (productId) {
          logAction({
            productId,
            action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringRedirected,
          });
        }

        setTimeout(() => {
          setIsRedirectingToAccountScoring(false);
        }, 2000);
      }
    } else {
      // Small-loan flow: open in new tab and trigger stepper
      if (accountScoringInvitationData) {
        const { response_url } = accountScoringInvitationData;
        window.open(String(response_url), '_blank');

        if (productId) {
          logAction({
            productId,
            action: PURCHASE_FLOW_LOG_ACTIONS.accountScoringRedirected,
          });
        }
      }

      setAccountScoringViewType(AccountScoringViewTypes.stepper);
      retriggerAccountScoringSteps();
    }
  };

  // EMTA consent request for credit-line flow
  const requestEmtaConsent = () => {
    if (!isCreditLineFlow || !user?.id) {
      throw new Error('User id is not defined');
    }

    const returnUrl = replaceOrAddQueryParamsInUrl({
      returnWithBase: true,
      url: `${pathname}${search}`,
      params: {
        [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
      },
    });

    getEmtaConsent({
      user_id: user.id,
      return_url: returnUrl,
      credit_account_id: productId,
    });
  };

  // EMTA redirect handler for credit-line flow
  const onEmtaRedirectButtonClick = () => {
    if (!isCreditLineFlow) return;

    if (emtaConsent?.url) {
      setIsRedirectingToEmta(true);
      window.location.href = emtaConsent?.url;

      if (productId) {
        logAction({
          productId,
          action: PURCHASE_FLOW_LOG_ACTIONS.emtaConsentRedirect,
        });
      }

      setTimeout(() => {
        setIsRedirectingToEmta(false);
      }, 2000);
    }
  };

  // Go back to scoring methods for credit-line flow
  const goBackToScoringMethods = () => {
    if (!isCreditLineFlow) return;

    removeFromStorage(LocalStorageKeys.accountScoringView);
    getPageUrlAndNavigate(false);
  };

  // Finish account scoring
  const finishAccountScoring = () => {
    finishAccountScoringSteps();
    removeFromStorage(LocalStorageKeys.accountScoringView);
    getPageUrlAndNavigate(true);
  };

  // Initialize data
  useEffectOnce(() => {
    if (isSmallLoanFlow) {
      getApplication();
    }
  });

  useEffectOnce(() => {
    if (
      isSmallLoanFlow &&
      accountScoringViewType === AccountScoringViewTypes.stepper
    ) {
      triggerAccountScoringSteps();
    }
  });

  // Store account scoring invitation and start polling
  useEffect(() => {
    if (productId && userId) {
      logAction({
        productId,
        action: PURCHASE_FLOW_LOG_ACTIONS.startingAccountScoring,
      });

      if (!email) {
        throw new Error('User email is missing');
      }
      if (!language) {
        throw new Error('Language is missing');
      }

      storeAccountScoringInvitation({
        user_id: userId,
        user_mail: email,
        user_name: userName,
        redirect_url: `${pathname}${search}`,
        language,
        ...productIdVariables,
      });

      startAccountScoringStatementPolling({
        user_id: userId,
        ...productIdVariables,
      });
    }
  }, [productId, userId]);

  // Handle account scoring completion
  useEffect(() => {
    if (accountScoringStatement?.process_status === 'FINISHED') {
      finishAccountScoring();
    }
  }, [accountScoringStatement?.process_status]);

  // Handle account scoring invitation completion
  useEffect(() => {
    if (accountScoringInvitation?.process_status === 'FINISHED') {
      finishAccountScoring();
    }
  }, [accountScoringInvitation?.process_status]);

  // Handle account scoring view type storage
  useEffect(() => {
    if (
      accountScoringViewType === AccountScoringViewType.SCORING ||
      accountScoringViewType === AccountScoringViewType.MANUAL_UPLOAD_SCORING
    ) {
      setToStorage(LocalStorageKeys.accountScoringView, accountScoringViewType);
    }
  }, [accountScoringViewType]);

  // Request EMTA consent for credit-line flow
  useEffectOnce(() => {
    if (isCreditLineFlow) {
      requestEmtaConsent();
    }
  });

  // Handle EMTA status polling for credit-line flow
  useEffect(() => {
    if (isCreditLineFlow && emtaStatus === EMTA_STATUSES.loading) {
      const returnUrl = replaceOrAddQueryParamsInUrl({
        returnWithBase: true,
        url: `${pathname}${search}`,
        params: {
          [AppSearchParams.emtaStatus]: EMTA_STATUSES.loading,
        },
      });

      if (!user?.id) {
        throw new Error('User id is not defined');
      }

      startEmtaConsentPolling({
        user_id: user.id,
        return_url: returnUrl,
      });
    }
  }, [emtaStatus, isCreditLineFlow]);

  return useMemo(
    () => ({
      // Common props
      accountScoringSteps,
      finishAccountScoring,
      onAccountStatementUploaded,
      accountScoringViewType,
      setAccountScoringViewType,
      onAccountScoringRedirectButtonClick,
      uploadAccountStatementProcessing,
      accountScoringUrl: accountScoringInvitationData?.response_url,
      accountScoringButtonDisabled: !accountScoringInvitationData?.response_url,

      // Credit-line specific props
      onEmtaRedirectButtonClick: isCreditLineFlow
        ? onEmtaRedirectButtonClick
        : undefined,
      isRedirectingToEmta,
      isRedirectingToAccountScoring,
      goBackToScoringMethods: isCreditLineFlow
        ? goBackToScoringMethods
        : undefined,
      isEmtaButtonDisabled: !emtaConsent?.url,

      // Small-loan specific props
      onAccessBankButtonClick: isSmallLoanFlow
        ? onAccountScoringRedirectButtonClick
        : () => {},
      retriggerAccountScoringSteps,
      accountScoringPageLoaded:
        !pageUrlAndNavigationProcessing &&
        Boolean(accountScoringInvitationData),
    }),
    [
      accountScoringSteps,
      finishAccountScoring,
      onAccountStatementUploaded,
      accountScoringViewType,
      onAccountScoringRedirectButtonClick,
      uploadAccountStatementProcessing,
      accountScoringInvitationData,
      onEmtaRedirectButtonClick,
      isRedirectingToEmta,
      goBackToScoringMethods,
      emtaConsent,
      retriggerAccountScoringSteps,
      pageUrlAndNavigationProcessing,
      isCreditLineFlow,
      isSmallLoanFlow,
    ],
  );
};
