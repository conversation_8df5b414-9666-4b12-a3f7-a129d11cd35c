import {
  FormFieldNames,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS,
  LocizeNamespaces,
  PageAttributeNames,
} from 'app-constants';
import { CheckboxWrapper } from 'components/checkbox-wrapper';
import { FormCheckboxField } from 'components/form/form-checkbox-field';
import { FormNumberInputField } from 'components/form/form-number-input-field';
import { FormSelectField } from 'components/form/form-select-field';
import { WarningNotification } from 'components/notification';
import { Button } from 'components/ui/button';
import { Form } from 'components/ui/form';
import { isLtRegion } from 'environment';
import type { Option } from 'models';
import type { ReactNode } from 'react';
import type { FieldValues, Path, UseFormReturn } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

export interface ContactExtraFormData extends FieldValues {
  [FormFieldNames.occupationCategory]?: string | null;
  [FormFieldNames.netIncomeMonthly]?: number | null;
  [FormFieldNames.expenditureMonthly]?: number | null;
  [FormFieldNames.monthlyLivingExpenses]?: number | null;
  [FormFieldNames.numberOfDependents]?: number | null;
  [FormFieldNames.totalExpenses]?: number | null;
  [FormFieldNames.employmentDate]?: string | null;
  [FormFieldNames.overdueDebt]?: number | null;
  [FormFieldNames.planningNewDebts]?: number | null;
  [FormFieldNames.futureReducedEarnings]?: number | null;
  [FormFieldNames.ultimateBeneficialOwner]?: boolean;
  [FormFieldNames.addLegalPersonToInvoice]?: boolean;
  [FormFieldNames.legalPerson]?: string | null;
}

export interface ContactExtraFormProps<TFormData extends ContactExtraFormData> {
  form: UseFormReturn<TFormData>;
  onSubmit: (data: TFormData) => void | Promise<void>;
  visiblePageAttributes: Record<string, boolean>;
  validationErrors: Record<string, boolean>;
  occupationCategoryOptions: Option[];
  employmentDateOptions: Option[];
  isSubmitting?: boolean;
  isNavigating?: boolean;
  onBack?: () => void;
  children?: ReactNode;
  className?: string;
}

export const ContactExtraForm = <TFormData extends FieldValues>({
  form,
  onSubmit,
  visiblePageAttributes,
  validationErrors,
  occupationCategoryOptions,
  employmentDateOptions,
  isSubmitting = false,
  isNavigating = false,
  onBack,
  children,
  className,
}: ContactExtraFormProps<TFormData>) => {
  const { t } = useTranslation(LocizeNamespaces.contactExtra);
  const { t: tc } = useTranslation(LocizeNamespaces.common);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={`grid w-full gap-2 ${className || ''}`}
      >
        {visiblePageAttributes[PageAttributeNames.checkIncomeDisclaimer] ? (
          <WarningNotification className="mb-10">
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.checkIncomeDisclaimer2)}
          </WarningNotification>
        ) : null}

        {visiblePageAttributes[
          PageAttributeNames.occupationCategoryDropdown
        ] ? (
          <FormSelectField<TFormData>
            control={form.control}
            name={FormFieldNames.occupationCategory as Path<TFormData>}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.occupationCategoryFieldLabel,
            )}
            options={occupationCategoryOptions}
            invalid={validationErrors[FormFieldNames.occupationCategory]}
            disabled={isSubmitting || !occupationCategoryOptions.length}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.netIncomeMonthly] ? (
          <FormNumberInputField<TFormData>
            control={form.control}
            disabled={isSubmitting}
            name={FormFieldNames.netIncomeMonthly as Path<TFormData>}
            label={t(
              isLtRegion
                ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabelLt
                : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeLabel,
            )}
            info={t(
              isLtRegion
                ? LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabelLt
                : LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.netIncomeTooltipLabel,
            )}
            invalid={validationErrors[FormFieldNames.netIncomeMonthly]}
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.expenditureMonthly] ? (
          <FormNumberInputField<TFormData>
            control={form.control}
            disabled={isSubmitting}
            name={FormFieldNames.expenditureMonthly as Path<TFormData>}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureLabel)}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.expenditureTooltipLabel,
            )}
            invalid={validationErrors[FormFieldNames.expenditureMonthly]}
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.monthlyLivingExpenses] ? (
          <FormNumberInputField<TFormData>
            control={form.control}
            disabled={isSubmitting}
            name={FormFieldNames.monthlyLivingExpenses as Path<TFormData>}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthLabel,
            )}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.livingExpensesPerMonthTooltipLabel,
            )}
            invalid={validationErrors[FormFieldNames.monthlyLivingExpenses]}
            suffix={'€'}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.numberOfDependents] ? (
          <FormNumberInputField<TFormData>
            control={form.control}
            name={FormFieldNames.numberOfDependents as Path<TFormData>}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.dependentsLabel)}
            allowLeadingZeros
            fixedDecimalScale={true}
            disabled={isSubmitting}
            invalid={validationErrors[FormFieldNames.numberOfDependents]}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.employmentDate] ? (
          <FormSelectField<TFormData>
            control={form.control}
            name={FormFieldNames.employmentDate as Path<TFormData>}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.employmentDateLabel)}
            options={employmentDateOptions}
            disabled={isSubmitting}
            invalid={validationErrors[FormFieldNames.employmentDate]}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.overdueDebt] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(FormFieldNames.overdueDebt as Path<TFormData>) ||
              validationErrors[FormFieldNames.overdueDebt]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(
                  FormFieldNames.overdueDebt as Path<TFormData>,
                  null as TFormData[Path<TFormData>],
                );
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtCheckboxLabel,
            )}
            disabled={isSubmitting}
            invalid={validationErrors[FormFieldNames.overdueDebt]}
          >
            <FormNumberInputField<TFormData>
              control={form.control}
              name={FormFieldNames.overdueDebt as Path<TFormData>}
              label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtLabel)}
              info={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.overdueDebtTooltipLabel,
              )}
              disabled={isSubmitting}
              invalid={validationErrors[FormFieldNames.overdueDebt]}
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.planningNewDebts] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(
                FormFieldNames.planningNewDebts as Path<TFormData>,
              ) || validationErrors[FormFieldNames.planningNewDebts]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(
                  FormFieldNames.planningNewDebts as Path<TFormData>,
                  null as TFormData[Path<TFormData>],
                );
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsCheckboxLabel,
            )}
            disabled={isSubmitting}
            invalid={validationErrors[FormFieldNames.planningNewDebts]}
          >
            <FormNumberInputField<TFormData>
              control={form.control}
              name={FormFieldNames.planningNewDebts as Path<TFormData>}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.planningNewDebtsLabel,
              )}
              disabled={isSubmitting}
              invalid={validationErrors[FormFieldNames.planningNewDebts]}
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.futureReducedEarnings] ? (
          <CheckboxWrapper
            className="mt-2"
            checked={
              !!form.watch(
                FormFieldNames.futureReducedEarnings as Path<TFormData>,
              ) || validationErrors[FormFieldNames.futureReducedEarnings]
            }
            onCheckedChange={(checked) => {
              if (!checked) {
                form.setValue(
                  FormFieldNames.futureReducedEarnings as Path<TFormData>,
                  null as TFormData[Path<TFormData>],
                );
              }
            }}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsCheckboxLabel,
            )}
            disabled={isSubmitting}
            invalid={validationErrors[FormFieldNames.futureReducedEarnings]}
          >
            <FormNumberInputField<TFormData>
              control={form.control}
              disabled={isSubmitting}
              name={FormFieldNames.futureReducedEarnings as Path<TFormData>}
              label={t(
                LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.futureReducedEarningsLabel,
              )}
              invalid={validationErrors[FormFieldNames.futureReducedEarnings]}
              suffix={'€'}
            />
          </CheckboxWrapper>
        ) : null}

        {visiblePageAttributes[PageAttributeNames.ultimateBeneficialOwner] ? (
          <FormCheckboxField<TFormData>
            containerClassName="mt-2 px-2.5"
            control={form.control}
            label={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerLabel,
            )}
            name={FormFieldNames.ultimateBeneficialOwner as Path<TFormData>}
            info={t(
              LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.ultimateBeneficialOwnerTooltip,
            )}
            disabled={isSubmitting}
            invalid={validationErrors[FormFieldNames.ultimateBeneficialOwner]}
          />
        ) : null}

        {visiblePageAttributes[PageAttributeNames.expenditureMonthly] &&
        visiblePageAttributes[PageAttributeNames.monthlyLivingExpenses] ? (
          <FormNumberInputField<TFormData>
            control={form.control}
            disabled={true}
            name={FormFieldNames.totalExpenses as Path<TFormData>}
            label={t(LOCIZE_CONTACT_EXTRA_TRANSLATION_KEYS.totalExpensesLabel)}
            suffix={'€'}
          />
        ) : null}

        {children}

        <Button
          className="mt-12"
          disabled={!isSubmitting && isNavigating}
          loading={isSubmitting}
          type="submit"
        >
          {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
        </Button>

        {onBack && (
          <Button
            fullWidth
            className="mt-2"
            variant="white"
            loading={!isSubmitting && isNavigating}
            disabled={isSubmitting}
            onClick={onBack}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.back)}
          </Button>
        )}
      </form>
    </Form>
  );
};
