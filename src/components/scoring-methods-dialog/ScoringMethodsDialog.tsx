import {
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES,
  AppRegions,
  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS,
  LOCIZE_COMMON_TRANSLATION_KEYS,
  LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { Dialog } from 'components/dialog';
import { Typography } from 'components/typography';
import { Button } from 'components/ui/button';
import { Input } from 'components/ui/input';
import { Separator } from 'components/ui/separator';
import { region } from 'environment';
import PaperClipIcon from 'icons/paper-clip.svg?react';
import TrashIcon from 'icons/trash.svg?react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { cn } from 'utils/tailwind';

const ACCEPTED_FILE_TYPES =
  ACCOUNT_SCORING_BANK_STATEMENT_ACCEPT_FILE_TYPES.join(', ');

export enum ScoringMethodsDialogMode {
  EMTA_CONSENT = 'emta-consent',
  ACCOUNT_SCORING = 'account-scoring',
}

type ScoringMethodsDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  mode: ScoringMethodsDialogMode;

  // EMTA-related props
  isRedirectingToAccountScoring?: boolean;
  onAccountScoringRedirectButtonClick?: () => void;
  accountScoringButtonDisabled?: boolean;

  // Account Scoring-related props
  isRedirectingToEmta?: boolean;
  onEmtaRedirectButtonClick?: () => void;
  isEmtaButtonDisabled?: boolean;
  uploadAccountStatementProcessing?: boolean;
  onAccountStatementUploaded?: (statement: File, callback?: () => void) => void;
};

const isEmtaSectionVisible = region === AppRegions.et;

export const ScoringMethodsDialog = ({
  open,
  onOpenChange,
  mode,
  isRedirectingToAccountScoring,
  onAccountScoringRedirectButtonClick,
  accountScoringButtonDisabled,
  isRedirectingToEmta,
  onEmtaRedirectButtonClick,
  isEmtaButtonDisabled,
  uploadAccountStatementProcessing,
  onAccountStatementUploaded,
}: ScoringMethodsDialogProps) => {
  const { t: tEmta } = useTranslation(LocizeNamespaces.emtaConsentV2);
  const { t: tAccount } = useTranslation(LocizeNamespaces.accountScoringV2);
  const { t: tc } = useTranslation(LocizeNamespaces.common);
  const [uploadedStatement, setUploadedStatement] = useState<File | null>(null);

  const isEmtaConsentMode = mode === ScoringMethodsDialogMode.EMTA_CONSENT;
  const isAccountScoringMode =
    mode === ScoringMethodsDialogMode.ACCOUNT_SCORING;

  const getTitle = () => {
    if (isEmtaConsentMode) {
      return tEmta(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.otherMethodsTitle);
    }
    return tAccount(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsTitle);
  };

  const getDescription = () => {
    if (isEmtaConsentMode) {
      return tEmta(
        LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.otherMethodsDescription,
      );
    }
    return tAccount(
      LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.otherMethodsDescription,
    );
  };

  const renderAccountScoringSection = () => {
    if (!isEmtaConsentMode && !onAccountScoringRedirectButtonClick) return null;

    return (
      <>
        <section className="space-y-4">
          <Typography variant="xxs">
            {isEmtaConsentMode
              ? tEmta(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.internetBankTitle)
              : tAccount(
                  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.accountScoringMethodTitle,
                )}
          </Typography>
          <Typography variant="text-s">
            {isEmtaConsentMode
              ? tEmta(
                  LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.internetBankDescription,
                )
              : tAccount(
                  LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.accountScoringMethodDescription,
                )}
          </Typography>
          <Button
            size="small"
            className="mt-6"
            loading={isRedirectingToAccountScoring}
            disabled={accountScoringButtonDisabled}
            onClick={() => {
              onAccountScoringRedirectButtonClick?.();
              onOpenChange(false);
            }}
          >
            {isEmtaConsentMode
              ? tEmta(LOCIZE_EMTA_CONSENT_TRANSLATION_KEYS.startButton)
              : tAccount(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.startButton)}
          </Button>
        </section>
        <Separator className="my-6" />
      </>
    );
  };

  const renderEmtaSection = () => {
    if (
      !isAccountScoringMode ||
      !isEmtaSectionVisible ||
      !onEmtaRedirectButtonClick
    )
      return null;

    return (
      <>
        <section className="space-y-4">
          <Typography variant="xxs">
            {tAccount(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.emtaTitle)}
          </Typography>
          <Typography variant="text-s">
            {tAccount(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.emtaDescription)}
          </Typography>
          <Button
            size="small"
            className="mt-6"
            loading={isRedirectingToEmta}
            disabled={isEmtaButtonDisabled}
            onClick={() => {
              onEmtaRedirectButtonClick();
              onOpenChange(false);
            }}
          >
            {tAccount(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.startButton)}
          </Button>
        </section>
        <Separator className="my-6" />
      </>
    );
  };

  const renderFileUploadSection = () => {
    if (!isAccountScoringMode || !onAccountStatementUploaded) return null;

    return (
      <section className="space-y-4">
        <Typography variant="xxs">
          {tAccount(
            LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadBankStatementTitle,
          )}
        </Typography>
        <Typography variant="text-s">
          {tAccount(
            LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadBankStatementDescription,
          )}
        </Typography>
        <Typography variant="text-s" className="text-neutral-500 mt-4">
          {tAccount(
            LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.acceptedFileTypesLabel,
            {
              acceptedFileTypes: ACCEPTED_FILE_TYPES,
            },
          )}
        </Typography>

        <div
          className={cn(
            'transition-all duration-500 ease-in-out max-h-[500px] opacity-100 overflow-hidden p-2 rounded-lg bg-neutral-50 border border-solid border-neutral-200 flex items-center gap-2 mt-6!',
            !uploadedStatement && 'max-h-0 opacity-0 py-0 mt-0!',
          )}
        >
          <PaperClipIcon />
          <Typography className="w-full" variant="text-s" affects="bold">
            {uploadedStatement?.name}
          </Typography>
          <Button
            size="small"
            className="px-2 ml-auto"
            variant="transparent"
            onClick={() => {
              setUploadedStatement(null);
              const input = document.getElementById('file-upload');
              if (input && input instanceof HTMLInputElement) {
                input.value = '';
              }
            }}
          >
            <TrashIcon />
          </Button>
        </div>

        <Input
          className="hidden"
          id="file-upload"
          type="file"
          accept={ACCEPTED_FILE_TYPES}
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) {
              setUploadedStatement(file);
            }
          }}
        />
        {!uploadedStatement && (
          <Button
            size="small"
            className="mt-6!"
            onClick={() => {
              document.getElementById('file-upload')?.click();
            }}
          >
            {tAccount(LOCIZE_ACCOUNT_SCORING_TRANSLATION_KEYS.uploadButton)}
          </Button>
        )}
        {uploadedStatement && (
          <Button
            size="small"
            className="mt-6!"
            loading={uploadAccountStatementProcessing}
            onClick={() => {
              onAccountStatementUploaded(uploadedStatement, () => {
                onOpenChange(false);
              });
            }}
          >
            {tc(LOCIZE_COMMON_TRANSLATION_KEYS.continue)}
          </Button>
        )}
      </section>
    );
  };

  return (
    <Dialog title={getTitle()} open={open} onOpenChange={onOpenChange}>
      <Typography>{getDescription()}</Typography>

      <Separator className="my-6" />

      {renderAccountScoringSection()}
      {renderEmtaSection()}
      {renderFileUploadSection()}
    </Dialog>
  );
};
